package com.returnmanager.di

import android.content.Context
import com.returnmanager.data.database.ReturnDatabase
import com.returnmanager.data.repository.ReturnRepository

/**
 * 应用程序依赖容器，用于管理应用程序级别的依赖
 */
class AppContainer(private val context: Context) {

    // 数据库实例 - 懒加载单例
    val database: ReturnDatabase by lazy {
        ReturnDatabase.getDatabase(context)
    }

    // Repository实例 - 懒加载单例
    val returnRepository: ReturnRepository by lazy {
        ReturnRepository(database.returnDao())
    }
}
