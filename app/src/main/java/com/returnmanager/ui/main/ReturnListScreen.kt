package com.returnmanager.ui.main

import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.material3.*
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import com.returnmanager.domain.model.Return
import com.returnmanager.domain.model.ReturnStatus
import com.returnmanager.ui.components.ReturnListItem

@Composable
fun ReturnListScreen(
    returns: List<Return>,
    onReturnClick: (Return) -> Unit,
    onReturnDelete: (Return) -> Unit,
    onReturnStatusChange: (Return, ReturnStatus) -> Unit,
    modifier: Modifier = Modifier
) {
    if (returns.isEmpty()) {
        Box(
            modifier = modifier.fillMaxSize(),
            contentAlignment = Alignment.Center
        ) {
            Column(
                horizontalAlignment = Alignment.CenterHorizontally
            ) {
                Text(
                    text = "暂无数据",
                    style = MaterialTheme.typography.bodyLarge,
                    color = MaterialTheme.colorScheme.onSurfaceVariant,
                    textAlign = TextAlign.Center
                )
                Spacer(modifier = Modifier.height(8.dp))
                Text(
                    text = "点击右上角 + 号添加退货记录",
                    style = MaterialTheme.typography.bodyMedium,
                    color = MaterialTheme.colorScheme.onSurfaceVariant,
                    textAlign = TextAlign.Center
                )
            }
        }
    } else {
        LazyColumn(
            modifier = modifier.fillMaxSize(),
            contentPadding = PaddingValues(vertical = 8.dp)
        ) {
            items(
                items = returns,
                key = { it.id }
            ) { returnItem ->
                ReturnListItem(
                    returnItem = returnItem,
                    onClick = { onReturnClick(returnItem) }
                )
            }
        }
    }
}
