package com.returnmanager.ui.main

import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.pager.HorizontalPager
import androidx.compose.foundation.pager.rememberPagerState
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.Add
import androidx.compose.material3.ExperimentalMaterial3Api
import androidx.compose.material3.Icon
import androidx.compose.material3.IconButton
import androidx.compose.material3.Scaffold
import androidx.compose.material3.Tab
import androidx.compose.material3.TabRow
import androidx.compose.material3.Text
import androidx.compose.material3.TopAppBar
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.compose.runtime.rememberCoroutineScope
import androidx.compose.ui.Modifier
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.tooling.preview.Preview
import androidx.lifecycle.viewmodel.compose.viewModel
import androidx.navigation.NavController
import androidx.navigation.compose.rememberNavController
import com.returnmanager.ReturnManagerApplication
import com.returnmanager.ui.viewmodel.MainViewModel
import com.returnmanager.ui.viewmodel.ViewModelFactory
import kotlinx.coroutines.launch

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun MainScreen(
    navController: NavController,
    viewModel: MainViewModel = viewModel(
        factory = ViewModelFactory(
            (LocalContext.current.applicationContext as ReturnManagerApplication).appContainer.returnRepository
        )
    )
) {
    val selectedTabIndex by viewModel.selectedTabIndex.collectAsState()
    val pendingReturns by viewModel.pendingReturns.collectAsState()
    val returnedReturns by viewModel.returnedReturns.collectAsState()
    val cancelledReturns by viewModel.cancelledReturns.collectAsState()

    MainScreenContent(
        navController = navController,
        selectedTabIndex = selectedTabIndex,
        pendingReturns = pendingReturns,
        returnedReturns = returnedReturns,
        cancelledReturns = cancelledReturns,
        onTabSelected = { viewModel.selectTab(it) },
        onAddReturn = { navController.navigate("add_return") },
        onReturnClick = { /* TODO: Navigate to detail */ },
        onReturnDelete = { viewModel.deleteReturn(it) },
        onReturnStatusChange = { returnItem, status ->
            viewModel.updateReturnStatus(returnItem, status)
        }
    )
}

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun MainScreenContent(
    navController: NavController,
    selectedTabIndex: Int,
    pendingReturns: List<com.returnmanager.domain.model.Return>,
    returnedReturns: List<com.returnmanager.domain.model.Return>,
    cancelledReturns: List<com.returnmanager.domain.model.Return>,
    onTabSelected: (Int) -> Unit,
    onAddReturn: () -> Unit,
    onReturnClick: (com.returnmanager.domain.model.Return) -> Unit,
    onReturnDelete: (com.returnmanager.domain.model.Return) -> Unit,
    onReturnStatusChange: (com.returnmanager.domain.model.Return, com.returnmanager.domain.model.ReturnStatus) -> Unit
) {
    val pagerState = rememberPagerState(pageCount = { 3 })
    val coroutineScope = rememberCoroutineScope()

    val tabs = listOf("未退款", "已退款", "已取消")

    LaunchedEffect(selectedTabIndex) {
        pagerState.animateScrollToPage(selectedTabIndex)
    }

    LaunchedEffect(pagerState.currentPage) {
        onTabSelected(pagerState.currentPage)
    }
    
    Scaffold(
        topBar = {
            TopAppBar(
                title = { Text("退货管理") },
                actions = {
                    IconButton(
                        onClick = onAddReturn
                    ) {
                        Icon(
                            imageVector = Icons.Default.Add,
                            contentDescription = "添加退货"
                        )
                    }
                }
            )
        }
    ) { paddingValues ->
        Column(
            modifier = Modifier
                .fillMaxSize()
                .padding(paddingValues)
        ) {
            TabRow(
                selectedTabIndex = selectedTabIndex,
                modifier = Modifier.fillMaxWidth()
            ) {
                tabs.forEachIndexed { index, title ->
                    Tab(
                        selected = selectedTabIndex == index,
                        onClick = {
                            onTabSelected(index)
                            coroutineScope.launch {
                                pagerState.animateScrollToPage(index)
                            }
                        },
                        text = { Text(title) }
                    )
                }
            }
            
            HorizontalPager(
                state = pagerState,
                modifier = Modifier.fillMaxSize()
            ) { page ->
                when (page) {
                    0 -> ReturnListScreen(
                        returns = pendingReturns,
                        onReturnClick = onReturnClick,
                        onReturnDelete = onReturnDelete,
                        onReturnStatusChange = onReturnStatusChange
                    )
                    1 -> ReturnListScreen(
                        returns = returnedReturns,
                        onReturnClick = onReturnClick,
                        onReturnDelete = onReturnDelete,
                        onReturnStatusChange = onReturnStatusChange
                    )
                    2 -> ReturnListScreen(
                        returns = cancelledReturns,
                        onReturnClick = onReturnClick,
                        onReturnDelete = onReturnDelete,
                        onReturnStatusChange = onReturnStatusChange
                    )
                }
            }
        }
    }
}

@Preview(showBackground = true)
@Composable
fun MainScreenPreview() {
    // 为预览创建一个简化版本，不依赖真实的ViewModel和数据库
    MainScreenContent(
        navController = rememberNavController(),
        selectedTabIndex = 0,
        pendingReturns = emptyList(),
        returnedReturns = emptyList(),
        cancelledReturns = emptyList(),
        onTabSelected = {},
        onAddReturn = {},
        onReturnClick = {},
        onReturnDelete = {},
        onReturnStatusChange = { _, _ -> }
    )
}
