package com.returnmanager.ui.viewmodel

import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.returnmanager.data.repository.ReturnRepository
import com.returnmanager.domain.model.Return
import com.returnmanager.domain.model.ReturnStatus
import kotlinx.coroutines.flow.*
import kotlinx.coroutines.launch

class MainViewModel(
    private val repository: ReturnRepository
) : ViewModel() {

    private val _selectedTabIndex = MutableStateFlow(0)
    val selectedTabIndex: StateFlow<Int> = _selectedTabIndex.asStateFlow()

    val pendingReturns: StateFlow<List<Return>> = repository
        .getReturnsByStatus(ReturnStatus.PENDING)
        .stateIn(
            scope = viewModelScope,
            started = SharingStarted.WhileSubscribed(5000),
            initialValue = emptyList()
        )

    val returnedReturns: StateFlow<List<Return>> = repository
        .getReturnsByStatus(ReturnStatus.RETURNED)
        .stateIn(
            scope = viewModelScope,
            started = SharingStarted.WhileSubscribed(5000),
            initialValue = emptyList()
        )

    val cancelledReturns: StateFlow<List<Return>> = repository
        .getReturnsByStatus(ReturnStatus.CANCELLED)
        .stateIn(
            scope = viewModelScope,
            started = SharingStarted.WhileSubscribed(5000),
            initialValue = emptyList()
        )

    fun selectTab(index: Int) {
        _selectedTabIndex.value = index
    }

    fun deleteReturn(returnItem: Return) {
        viewModelScope.launch {
            repository.deleteReturn(returnItem)
        }
    }

    fun updateReturnStatus(returnItem: Return, newStatus: ReturnStatus) {
        viewModelScope.launch {
            repository.updateReturn(returnItem.copy(returnStatus = newStatus))
        }
    }
}
