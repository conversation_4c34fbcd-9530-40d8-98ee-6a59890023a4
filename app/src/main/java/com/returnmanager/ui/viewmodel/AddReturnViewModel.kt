package com.returnmanager.ui.viewmodel

import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.returnmanager.data.repository.ReturnRepository
import com.returnmanager.domain.model.Return
import com.returnmanager.domain.model.ReturnStatus
import com.returnmanager.domain.model.ShoppingPlatform
import com.returnmanager.utils.DateUtils
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.launch
import java.util.Date

class AddReturnViewModel(
    private val repository: ReturnRepository
) : ViewModel() {
    
    private val _uiState = MutableStateFlow(AddReturnUiState())
    val uiState: StateFlow<AddReturnUiState> = _uiState.asStateFlow()
    
    private val _isLoading = MutableStateFlow(false)
    val isLoading: StateFlow<Boolean> = _isLoading.asStateFlow()
    
    private val _saveResult = MutableStateFlow<SaveResult?>(null)
    val saveResult: StateFlow<SaveResult?> = _saveResult.asStateFlow()
    
    fun updateSerialNumber(value: String) {
        _uiState.value = _uiState.value.copy(serialNumber = value)
    }
    
    fun updateProductName(value: String) {
        _uiState.value = _uiState.value.copy(productName = value)
    }
    
    fun updateProductAmount(value: String) {
        _uiState.value = _uiState.value.copy(productAmount = value)
    }
    
    fun updateHasShippingInsurance(value: Boolean) {
        _uiState.value = _uiState.value.copy(hasShippingInsurance = value)
    }
    
    fun updateReturnApplied(value: Boolean) {
        _uiState.value = _uiState.value.copy(returnApplied = value)
    }
    
    fun updateShoppingPlatform(value: ShoppingPlatform) {
        _uiState.value = _uiState.value.copy(shoppingPlatform = value)
    }
    
    fun updateReturnDate(value: Date) {
        _uiState.value = _uiState.value.copy(returnDate = value)
    }
    
    fun updateAdvanceShippingFee(value: String) {
        _uiState.value = _uiState.value.copy(advanceShippingFee = value)
    }
    
    fun updateReturnStatus(value: ReturnStatus) {
        _uiState.value = _uiState.value.copy(returnStatus = value)
    }
    
    fun setClipboardSuggestion(text: String?) {
        _uiState.value = _uiState.value.copy(clipboardSuggestion = text)
    }
    
    fun applyClipboardSuggestion() {
        _uiState.value.clipboardSuggestion?.let { suggestion ->
            updateProductName(suggestion)
            _uiState.value = _uiState.value.copy(clipboardSuggestion = null)
        }
    }
    
    fun saveReturn() {
        val state = _uiState.value
        
        if (!isValidInput(state)) {
            _saveResult.value = SaveResult.Error("请填写所有必填字段")
            return
        }
        
        _isLoading.value = true
        
        viewModelScope.launch {
            try {
                val returnItem = Return(
                    serialNumber = state.serialNumber,
                    productName = state.productName,
                    productAmount = state.productAmount.toDoubleOrNull() ?: 0.0,
                    hasShippingInsurance = state.hasShippingInsurance,
                    returnApplied = state.returnApplied,
                    shoppingPlatform = state.shoppingPlatform.displayName,
                    returnDate = state.returnDate,
                    advanceShippingFee = state.advanceShippingFee.toDoubleOrNull() ?: 0.0,
                    returnStatus = state.returnStatus
                )
                
                repository.insertReturn(returnItem)
                _saveResult.value = SaveResult.Success
            } catch (e: Exception) {
                _saveResult.value = SaveResult.Error("保存失败: ${e.message}")
            } finally {
                _isLoading.value = false
            }
        }
    }
    
    private fun isValidInput(state: AddReturnUiState): Boolean {
        return state.serialNumber.isNotBlank() &&
                state.productName.isNotBlank() &&
                state.productAmount.isNotBlank()
    }
    
    fun clearSaveResult() {
        _saveResult.value = null
    }
}

data class AddReturnUiState(
    val serialNumber: String = "",
    val productName: String = "",
    val productAmount: String = "",
    val hasShippingInsurance: Boolean = false,
    val returnApplied: Boolean = false,
    val shoppingPlatform: ShoppingPlatform = ShoppingPlatform.TAOBAO,
    val returnDate: Date = DateUtils.getCurrentDate(),
    val advanceShippingFee: String = "",
    val returnStatus: ReturnStatus = ReturnStatus.PENDING,
    val clipboardSuggestion: String? = null
)

sealed class SaveResult {
    object Success : SaveResult()
    data class Error(val message: String) : SaveResult()
}
