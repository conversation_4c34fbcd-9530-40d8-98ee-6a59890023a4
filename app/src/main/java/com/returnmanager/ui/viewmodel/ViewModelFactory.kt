package com.returnmanager.ui.viewmodel

import androidx.lifecycle.ViewModel
import androidx.lifecycle.ViewModelProvider
import com.returnmanager.data.repository.ReturnRepository

/**
 * ViewModelFactory用于创建需要依赖注入的ViewModel
 */
class ViewModelFactory(
    private val returnRepository: ReturnRepository
) : ViewModelProvider.Factory {

    @Suppress("UNCHECKED_CAST")
    override fun <T : ViewModel> create(modelClass: Class<T>): T {
        return when {
            modelClass.isAssignableFrom(MainViewModel::class.java) -> {
                MainViewModel(returnRepository) as T
            }
            modelClass.isAssignableFrom(AddReturnViewModel::class.java) -> {
                AddReturnViewModel(returnRepository) as T
            }
            else -> throw IllegalArgumentException("Unknown ViewModel class: ${modelClass.name}")
        }
    }
}
