package com.returnmanager.data.dao

import androidx.room.*
import com.returnmanager.data.entity.ReturnEntity
import kotlinx.coroutines.flow.Flow

@Dao
interface ReturnDao {
    
    @Query("SELECT * FROM returns WHERE returnStatus = :status ORDER BY returnDate DESC")
    fun getReturnsByStatus(status: String): Flow<List<ReturnEntity>>
    
    @Query("SELECT * FROM returns ORDER BY returnDate DESC")
    fun getAllReturns(): Flow<List<ReturnEntity>>
    
    @Query("SELECT * FROM returns WHERE id = :id")
    suspend fun getReturnById(id: Long): ReturnEntity?
    
    @Insert(onConflict = OnConflictStrategy.REPLACE)
    suspend fun insertReturn(returnItem: ReturnEntity): Long
    
    @Update
    suspend fun updateReturn(returnItem: ReturnEntity)
    
    @Delete
    suspend fun deleteReturn(returnItem: ReturnEntity)
    
    @Query("DELETE FROM returns WHERE id = :id")
    suspend fun deleteReturnById(id: Long)
}
