package com.returnmanager.data.repository

import com.returnmanager.data.dao.ReturnDao
import com.returnmanager.data.entity.ReturnEntity
import com.returnmanager.domain.model.Return
import com.returnmanager.domain.model.ReturnStatus
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.map

class ReturnRepository(
    private val returnDao: ReturnDao
) {

    fun getReturnsByStatus(status: ReturnStatus): Flow<List<Return>> {
        return returnDao.getReturnsByStatus(status.displayName)
            .map { entities -> entities.map { it.toDomainModel() } }
    }

    fun getAllReturns(): Flow<List<Return>> {
        return returnDao.getAllReturns()
            .map { entities -> entities.map { it.toDomainModel() } }
    }

    suspend fun getReturnById(id: Long): Return? {
        return returnDao.getReturnById(id)?.toDomainModel()
    }

    suspend fun insertReturn(returnItem: Return): Long {
        return returnDao.insertReturn(returnItem.toEntity())
    }

    suspend fun updateReturn(returnItem: Return) {
        returnDao.updateReturn(returnItem.toEntity())
    }

    suspend fun deleteReturn(returnItem: Return) {
        returnDao.deleteReturn(returnItem.toEntity())
    }

    suspend fun deleteReturnById(id: Long) {
        returnDao.deleteReturnById(id)
    }

    private fun ReturnEntity.toDomainModel(): Return {
        return Return(
            id = id,
            serialNumber = serialNumber,
            productName = productName,
            productAmount = productAmount,
            hasShippingInsurance = hasShippingInsurance,
            returnApplied = returnApplied,
            shoppingPlatform = shoppingPlatform,
            returnDate = returnDate,
            advanceShippingFee = advanceShippingFee,
            returnStatus = ReturnStatus.fromDisplayName(returnStatus),
            createdAt = createdAt,
            updatedAt = updatedAt
        )
    }

    private fun Return.toEntity(): ReturnEntity {
        return ReturnEntity(
            id = id,
            serialNumber = serialNumber,
            productName = productName,
            productAmount = productAmount,
            hasShippingInsurance = hasShippingInsurance,
            returnApplied = returnApplied,
            shoppingPlatform = shoppingPlatform,
            returnDate = returnDate,
            advanceShippingFee = advanceShippingFee,
            returnStatus = returnStatus.displayName,
            createdAt = createdAt,
            updatedAt = updatedAt
        )
    }
}
