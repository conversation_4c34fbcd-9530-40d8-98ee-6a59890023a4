package com.returnmanager.data.database

import androidx.room.Database
import androidx.room.Room
import androidx.room.RoomDatabase
import androidx.room.TypeConverters
import android.content.Context
import com.returnmanager.data.dao.ReturnDao
import com.returnmanager.data.entity.ReturnEntity

@Database(
    entities = [ReturnEntity::class],
    version = 1,
    exportSchema = false
)
@TypeConverters(Converters::class)
abstract class ReturnDatabase : RoomDatabase() {
    
    abstract fun returnDao(): ReturnDao
    
    companion object {
        @Volatile
        private var INSTANCE: ReturnDatabase? = null
        
        fun getDatabase(context: Context): ReturnDatabase {
            return INSTANCE ?: synchronized(this) {
                val instance = Room.databaseBuilder(
                    context.applicationContext,
                    ReturnDatabase::class.java,
                    "return_database"
                ).build()
                INSTANCE = instance
                instance
            }
        }
    }
}
