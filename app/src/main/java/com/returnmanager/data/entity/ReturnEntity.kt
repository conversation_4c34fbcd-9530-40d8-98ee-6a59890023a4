package com.returnmanager.data.entity

import androidx.room.Entity
import androidx.room.PrimaryKey
import java.util.Date

@Entity(tableName = "returns")
data class ReturnEntity(
    @PrimaryKey(autoGenerate = true)
    val id: Long = 0,
    val serialNumber: String,
    val productName: String,
    val productAmount: Double,
    val hasShippingInsurance: Boolean,
    val returnApplied: Boolean,
    val shoppingPlatform: String,
    val returnDate: Date,
    val advanceShippingFee: Double,
    val returnStatus: String, // "已退款", "未退款", "已取消"
    val createdAt: Date = Date(),
    val updatedAt: Date = Date()
)
