package com.returnmanager.domain.model

import android.os.Parcelable
import kotlinx.parcelize.Parcelize
import java.util.Date

@Parcelize
data class Return(
    val id: Long = 0,
    val serialNumber: String,
    val productName: String,
    val productAmount: Double,
    val hasShippingInsurance: <PERSON>olean,
    val returnApplied: Boolean,
    val shoppingPlatform: String,
    val returnDate: Date,
    val advanceShippingFee: Double,
    val returnStatus: ReturnStatus,
    val createdAt: Date = Date(),
    val updatedAt: Date = Date()
) : Parcelable

enum class ReturnStatus(val displayName: String) {
    PENDING("未退款"),
    RETURNED("已退款"),
    CANCELLED("已取消");
    
    companion object {
        fun fromDisplayName(displayName: String): ReturnStatus {
            return values().find { it.displayName == displayName } ?: PENDING
        }
    }
}

enum class ShoppingPlatform(val displayName: String) {
    TAOBAO("淘宝"),
    JD("京东"),
    PDD("拼多多"),
    DOUYIN("抖音");
    
    companion object {
        fun fromDisplayName(displayName: String): ShoppingPlatform {
            return values().find { it.displayName == displayName } ?: TAOBAO
        }
    }
}
