package com.refundmanager.di

import android.content.Context
import com.refundmanager.data.database.RefundDatabase
import com.refundmanager.data.repository.RefundRepository

/**
 * 应用程序依赖容器，用于管理应用程序级别的依赖
 */
class AppContainer(private val context: Context) {
    
    // 数据库实例 - 懒加载单例
    val database: RefundDatabase by lazy {
        RefundDatabase.getDatabase(context)
    }
    
    // Repository实例 - 懒加载单例
    val refundRepository: RefundRepository by lazy {
        RefundRepository(database.refundDao())
    }
}
