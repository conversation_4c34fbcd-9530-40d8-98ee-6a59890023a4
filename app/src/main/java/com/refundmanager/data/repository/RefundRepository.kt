package com.refundmanager.data.repository

import com.refundmanager.data.dao.RefundDao
import com.refundmanager.data.entity.RefundEntity
import com.refundmanager.domain.model.Refund
import com.refundmanager.domain.model.RefundStatus
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.map
class RefundRepository(
    private val refundDao: RefundDao
) {
    
    fun getRefundsByStatus(status: RefundStatus): Flow<List<Refund>> {
        return refundDao.getRefundsByStatus(status.displayName)
            .map { entities -> entities.map { it.toDomainModel() } }
    }
    
    fun getAllRefunds(): Flow<List<Refund>> {
        return refundDao.getAllRefunds()
            .map { entities -> entities.map { it.toDomainModel() } }
    }
    
    suspend fun getRefundById(id: Long): Refund? {
        return refundDao.getRefundById(id)?.toDomainModel()
    }
    
    suspend fun insertRefund(refund: Refund): Long {
        return refundDao.insertRefund(refund.toEntity())
    }
    
    suspend fun updateRefund(refund: Refund) {
        refundDao.updateRefund(refund.toEntity())
    }
    
    suspend fun deleteRefund(refund: Refund) {
        refundDao.deleteRefund(refund.toEntity())
    }
    
    suspend fun deleteRefundById(id: Long) {
        refundDao.deleteRefundById(id)
    }
    
    private fun RefundEntity.toDomainModel(): Refund {
        return Refund(
            id = id,
            serialNumber = serialNumber,
            productName = productName,
            productAmount = productAmount,
            hasShippingInsurance = hasShippingInsurance,
            refundApplied = refundApplied,
            shoppingPlatform = shoppingPlatform,
            refundDate = refundDate,
            advanceShippingFee = advanceShippingFee,
            refundStatus = RefundStatus.fromDisplayName(refundStatus),
            createdAt = createdAt,
            updatedAt = updatedAt
        )
    }
    
    private fun Refund.toEntity(): RefundEntity {
        return RefundEntity(
            id = id,
            serialNumber = serialNumber,
            productName = productName,
            productAmount = productAmount,
            hasShippingInsurance = hasShippingInsurance,
            refundApplied = refundApplied,
            shoppingPlatform = shoppingPlatform,
            refundDate = refundDate,
            advanceShippingFee = advanceShippingFee,
            refundStatus = refundStatus.displayName,
            createdAt = createdAt,
            updatedAt = updatedAt
        )
    }
}
