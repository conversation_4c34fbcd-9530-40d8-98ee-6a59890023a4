package com.refundmanager.ui.add

import androidx.compose.foundation.layout.*
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.text.KeyboardOptions
import androidx.compose.foundation.verticalScroll
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.ArrowBack
import androidx.compose.material.icons.filled.ContentPaste
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.focus.onFocusChanged
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.text.input.KeyboardType
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.lifecycle.viewmodel.compose.viewModel
import androidx.navigation.NavController
import androidx.navigation.compose.rememberNavController
import com.refundmanager.RefundManagerApplication
import com.refundmanager.domain.model.RefundStatus
import com.refundmanager.domain.model.ShoppingPlatform
import com.refundmanager.ui.components.DatePickerField
import com.refundmanager.ui.components.DropdownField
import com.refundmanager.ui.viewmodel.AddRefundViewModel
import com.refundmanager.ui.viewmodel.SaveResult
import com.refundmanager.ui.viewmodel.ViewModelFactory
import com.refundmanager.utils.ClipboardUtils
import java.util.Date

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun AddRefundScreen(
    navController: NavController,
    viewModel: AddRefundViewModel = viewModel(
        factory = ViewModelFactory(
            (LocalContext.current.applicationContext as RefundManagerApplication).appContainer.refundRepository
        )
    )
) {
    val context = LocalContext.current
    val uiState by viewModel.uiState.collectAsState()
    val isLoading by viewModel.isLoading.collectAsState()
    val saveResult by viewModel.saveResult.collectAsState()

    LaunchedEffect(Unit) {
        val clipboardText = ClipboardUtils.getClipboardText(context)
        viewModel.setClipboardSuggestion(clipboardText)
    }

    LaunchedEffect(saveResult) {
        when (saveResult) {
            is SaveResult.Success -> {
                navController.popBackStack()
            }
            is SaveResult.Error -> {
                // Error will be shown in UI
            }
            null -> {}
        }
    }

    AddRefundScreenContent(
        navController = navController,
        serialNumber = uiState.serialNumber,
        productName = uiState.productName,
        productAmount = uiState.productAmount,
        hasShippingInsurance = uiState.hasShippingInsurance,
        refundApplied = uiState.refundApplied,
        shoppingPlatform = uiState.shoppingPlatform,
        refundDate = uiState.refundDate,
        advanceShippingFee = uiState.advanceShippingFee,
        refundStatus = uiState.refundStatus,
        clipboardSuggestion = uiState.clipboardSuggestion,
        isLoading = isLoading,
        saveResult = saveResult,
        onSerialNumberChange = viewModel::updateSerialNumber,
        onProductNameChange = viewModel::updateProductName,
        onProductAmountChange = viewModel::updateProductAmount,
        onHasShippingInsuranceChange = viewModel::updateHasShippingInsurance,
        onRefundAppliedChange = viewModel::updateRefundApplied,
        onShoppingPlatformChange = viewModel::updateShoppingPlatform,
        onRefundDateChange = viewModel::updateRefundDate,
        onAdvanceShippingFeeChange = viewModel::updateAdvanceShippingFee,
        onRefundStatusChange = viewModel::updateRefundStatus,
        onApplyClipboardSuggestion = viewModel::applyClipboardSuggestion,
        onSaveRefund = viewModel::saveRefund,
        onBack = { navController.popBackStack() }
    )
}

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun AddRefundScreenContent(
    navController: NavController,
    serialNumber: String,
    productName: String,
    productAmount: String,
    hasShippingInsurance: Boolean,
    refundApplied: Boolean,
    shoppingPlatform: ShoppingPlatform,
    refundDate: Date,
    advanceShippingFee: String,
    refundStatus: RefundStatus,
    clipboardSuggestion: String?,
    isLoading: Boolean,
    saveResult: SaveResult?,
    onSerialNumberChange: (String) -> Unit,
    onProductNameChange: (String) -> Unit,
    onProductAmountChange: (String) -> Unit,
    onHasShippingInsuranceChange: (Boolean) -> Unit,
    onRefundAppliedChange: (Boolean) -> Unit,
    onShoppingPlatformChange: (ShoppingPlatform) -> Unit,
    onRefundDateChange: (Date) -> Unit,
    onAdvanceShippingFeeChange: (String) -> Unit,
    onRefundStatusChange: (RefundStatus) -> Unit,
    onApplyClipboardSuggestion: () -> Unit,
    onSaveRefund: () -> Unit,
    onBack: () -> Unit
) {
    
    Scaffold(
        topBar = {
            TopAppBar(
                title = { Text("新增退货") },
                navigationIcon = {
                    IconButton(onClick = onBack) {
                        Icon(Icons.Default.ArrowBack, contentDescription = "返回")
                    }
                }
            )
        }
    ) { paddingValues ->
        Column(
            modifier = Modifier
                .fillMaxSize()
                .padding(paddingValues)
                .verticalScroll(rememberScrollState())
                .padding(16.dp),
            verticalArrangement = Arrangement.spacedBy(16.dp)
        ) {
            // Serial Number
            OutlinedTextField(
                value = serialNumber,
                onValueChange = onSerialNumberChange,
                label = { Text("序号") },
                keyboardOptions = KeyboardOptions(keyboardType = KeyboardType.Number),
                modifier = Modifier.fillMaxWidth()
            )
            
            // Product Name with clipboard suggestion
            Column {
                Row(
                    modifier = Modifier.fillMaxWidth(),
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    OutlinedTextField(
                        value = productName,
                        onValueChange = onProductNameChange,
                        label = { Text("商品名称") },
                        modifier = Modifier.weight(1f)
                    )

                    clipboardSuggestion?.let { suggestion ->
                        Spacer(modifier = Modifier.width(8.dp))
                        TextButton(
                            onClick = onApplyClipboardSuggestion,
                            modifier = Modifier.wrapContentWidth()
                        ) {
                            Icon(
                                Icons.Default.ContentPaste,
                                contentDescription = null,
                                modifier = Modifier.size(16.dp)
                            )
                            Spacer(modifier = Modifier.width(4.dp))
                            Text(
                                text = if (suggestion.length > 10) 
                                    "${suggestion.take(10)}..." 
                                else suggestion,
                                maxLines = 1
                            )
                        }
                    }
                }
            }
            
            // Product Amount
            OutlinedTextField(
                value = productAmount,
                onValueChange = onProductAmountChange,
                label = { Text("商品金额") },
                keyboardOptions = KeyboardOptions(keyboardType = KeyboardType.Decimal),
                modifier = Modifier.fillMaxWidth()
            )

            // Has Shipping Insurance
            DropdownField(
                label = "是否有运费险",
                value = if (hasShippingInsurance) "有" else "无",
                options = listOf("有", "无"),
                onValueChange = { onHasShippingInsuranceChange(it == "有") }
            )

            // Refund Applied
            DropdownField(
                label = "是否申请退货",
                value = if (refundApplied) "已申请" else "未申请",
                options = listOf("已申请", "未申请"),
                onValueChange = { onRefundAppliedChange(it == "已申请") }
            )

            // Shopping Platform
            DropdownField(
                label = "购物平台",
                value = shoppingPlatform.displayName,
                options = ShoppingPlatform.values().map { it.displayName },
                onValueChange = {
                    onShoppingPlatformChange(ShoppingPlatform.fromDisplayName(it))
                }
            )

            // Refund Date
            DatePickerField(
                label = "退货日期",
                date = refundDate,
                onDateChange = onRefundDateChange
            )

            // Advance Shipping Fee
            OutlinedTextField(
                value = advanceShippingFee,
                onValueChange = onAdvanceShippingFeeChange,
                label = { Text("垫付运费") },
                keyboardOptions = KeyboardOptions(keyboardType = KeyboardType.Decimal),
                modifier = Modifier.fillMaxWidth()
            )

            // Refund Status
            DropdownField(
                label = "是否退款",
                value = refundStatus.displayName,
                options = listOf(RefundStatus.REFUNDED.displayName, RefundStatus.PENDING.displayName),
                onValueChange = {
                    onRefundStatusChange(RefundStatus.fromDisplayName(it))
                }
            )
            
            Spacer(modifier = Modifier.height(16.dp))
            
            // Submit Button
            Button(
                onClick = onSaveRefund,
                enabled = !isLoading,
                modifier = Modifier
                    .fillMaxWidth()
                    .height(48.dp)
            ) {
                if (isLoading) {
                    CircularProgressIndicator(
                        modifier = Modifier.size(20.dp),
                        color = MaterialTheme.colorScheme.onPrimary
                    )
                } else {
                    Text("提交")
                }
            }

            // Error message
            saveResult?.let { result ->
                if (result is SaveResult.Error) {
                    Card(
                        modifier = Modifier.fillMaxWidth(),
                        colors = CardDefaults.cardColors(
                            containerColor = MaterialTheme.colorScheme.errorContainer
                        )
                    ) {
                        Text(
                            text = result.message,
                            modifier = Modifier.padding(16.dp),
                            color = MaterialTheme.colorScheme.onErrorContainer
                        )
                    }
                }
            }
        }
    }
}

@Preview(showBackground = true)
@Composable
fun AddRefundScreenPreview() {
    AddRefundScreenContent(
        navController = rememberNavController(),
        serialNumber = "001",
        productName = "示例商品",
        productAmount = "99.99",
        hasShippingInsurance = true,
        refundApplied = true,
        shoppingPlatform = ShoppingPlatform.TAOBAO,
        refundDate = Date(),
        advanceShippingFee = "10.00",
        refundStatus = RefundStatus.PENDING,
        clipboardSuggestion = null,
        isLoading = false,
        saveResult = null,
        onSerialNumberChange = {},
        onProductNameChange = {},
        onProductAmountChange = {},
        onHasShippingInsuranceChange = {},
        onRefundAppliedChange = {},
        onShoppingPlatformChange = {},
        onRefundDateChange = {},
        onAdvanceShippingFeeChange = {},
        onRefundStatusChange = {},
        onApplyClipboardSuggestion = {},
        onSaveRefund = {},
        onBack = {}
    )
}
