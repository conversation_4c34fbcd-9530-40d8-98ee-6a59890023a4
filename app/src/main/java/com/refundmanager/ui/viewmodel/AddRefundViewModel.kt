package com.refundmanager.ui.viewmodel

import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.refundmanager.data.repository.RefundRepository
import com.refundmanager.domain.model.Refund
import com.refundmanager.domain.model.RefundStatus
import com.refundmanager.domain.model.ShoppingPlatform
import com.refundmanager.utils.DateUtils
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.launch
import java.util.Date

class AddRefundViewModel(
    private val repository: RefundRepository
) : ViewModel() {
    
    private val _uiState = MutableStateFlow(AddRefundUiState())
    val uiState: StateFlow<AddRefundUiState> = _uiState.asStateFlow()
    
    private val _isLoading = MutableStateFlow(false)
    val isLoading: StateFlow<Boolean> = _isLoading.asStateFlow()
    
    private val _saveResult = MutableStateFlow<SaveResult?>(null)
    val saveResult: StateFlow<SaveResult?> = _saveResult.asStateFlow()
    
    fun updateSerialNumber(value: String) {
        _uiState.value = _uiState.value.copy(serialNumber = value)
    }
    
    fun updateProductName(value: String) {
        _uiState.value = _uiState.value.copy(productName = value)
    }
    
    fun updateProductAmount(value: String) {
        _uiState.value = _uiState.value.copy(productAmount = value)
    }
    
    fun updateHasShippingInsurance(value: Boolean) {
        _uiState.value = _uiState.value.copy(hasShippingInsurance = value)
    }
    
    fun updateRefundApplied(value: Boolean) {
        _uiState.value = _uiState.value.copy(refundApplied = value)
    }
    
    fun updateShoppingPlatform(value: ShoppingPlatform) {
        _uiState.value = _uiState.value.copy(shoppingPlatform = value)
    }
    
    fun updateRefundDate(value: Date) {
        _uiState.value = _uiState.value.copy(refundDate = value)
    }
    
    fun updateAdvanceShippingFee(value: String) {
        _uiState.value = _uiState.value.copy(advanceShippingFee = value)
    }
    
    fun updateRefundStatus(value: RefundStatus) {
        _uiState.value = _uiState.value.copy(refundStatus = value)
    }
    
    fun setClipboardSuggestion(text: String?) {
        _uiState.value = _uiState.value.copy(clipboardSuggestion = text)
    }
    
    fun applyClipboardSuggestion() {
        _uiState.value.clipboardSuggestion?.let { suggestion ->
            updateProductName(suggestion)
            _uiState.value = _uiState.value.copy(clipboardSuggestion = null)
        }
    }
    
    fun saveRefund() {
        val state = _uiState.value
        
        if (!isValidInput(state)) {
            _saveResult.value = SaveResult.Error("请填写所有必填字段")
            return
        }
        
        _isLoading.value = true
        
        viewModelScope.launch {
            try {
                val refund = Refund(
                    serialNumber = state.serialNumber,
                    productName = state.productName,
                    productAmount = state.productAmount.toDoubleOrNull() ?: 0.0,
                    hasShippingInsurance = state.hasShippingInsurance,
                    refundApplied = state.refundApplied,
                    shoppingPlatform = state.shoppingPlatform.displayName,
                    refundDate = state.refundDate,
                    advanceShippingFee = state.advanceShippingFee.toDoubleOrNull() ?: 0.0,
                    refundStatus = state.refundStatus
                )
                
                repository.insertRefund(refund)
                _saveResult.value = SaveResult.Success
            } catch (e: Exception) {
                _saveResult.value = SaveResult.Error("保存失败: ${e.message}")
            } finally {
                _isLoading.value = false
            }
        }
    }
    
    private fun isValidInput(state: AddRefundUiState): Boolean {
        return state.serialNumber.isNotBlank() &&
                state.productName.isNotBlank() &&
                state.productAmount.isNotBlank()
    }
    
    fun clearSaveResult() {
        _saveResult.value = null
    }
}

data class AddRefundUiState(
    val serialNumber: String = "",
    val productName: String = "",
    val productAmount: String = "",
    val hasShippingInsurance: Boolean = false,
    val refundApplied: Boolean = false,
    val shoppingPlatform: ShoppingPlatform = ShoppingPlatform.TAOBAO,
    val refundDate: Date = DateUtils.getCurrentDate(),
    val advanceShippingFee: String = "",
    val refundStatus: RefundStatus = RefundStatus.PENDING,
    val clipboardSuggestion: String? = null
)

sealed class SaveResult {
    object Success : SaveResult()
    data class Error(val message: String) : SaveResult()
}
