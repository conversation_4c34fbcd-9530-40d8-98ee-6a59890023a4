package com.refundmanager.ui.viewmodel

import androidx.lifecycle.ViewModel
import androidx.lifecycle.ViewModelProvider
import com.refundmanager.data.repository.RefundRepository

/**
 * ViewModelFactory用于创建需要依赖注入的ViewModel
 */
class ViewModelFactory(
    private val refundRepository: RefundRepository
) : ViewModelProvider.Factory {
    
    @Suppress("UNCHECKED_CAST")
    override fun <T : ViewModel> create(modelClass: Class<T>): T {
        return when {
            modelClass.isAssignableFrom(MainViewModel::class.java) -> {
                MainViewModel(refundRepository) as T
            }
            modelClass.isAssignableFrom(AddRefundViewModel::class.java) -> {
                AddRefundViewModel(refundRepository) as T
            }
            else -> throw IllegalArgumentException("Unknown ViewModel class: ${modelClass.name}")
        }
    }
}
