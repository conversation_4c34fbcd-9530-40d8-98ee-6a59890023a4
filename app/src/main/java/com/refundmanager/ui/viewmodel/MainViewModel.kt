package com.refundmanager.ui.viewmodel

import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.refundmanager.data.repository.RefundRepository
import com.refundmanager.domain.model.Refund
import com.refundmanager.domain.model.RefundStatus
import kotlinx.coroutines.flow.*
import kotlinx.coroutines.launch

class MainViewModel(
    private val repository: RefundRepository
) : ViewModel() {
    
    private val _selectedTabIndex = MutableStateFlow(0)
    val selectedTabIndex: StateFlow<Int> = _selectedTabIndex.asStateFlow()
    
    val pendingRefunds: StateFlow<List<Refund>> = repository
        .getRefundsByStatus(RefundStatus.PENDING)
        .stateIn(
            scope = viewModelScope,
            started = SharingStarted.WhileSubscribed(5000),
            initialValue = emptyList()
        )
    
    val refundedRefunds: StateFlow<List<Refund>> = repository
        .getRefundsByStatus(RefundStatus.REFUNDED)
        .stateIn(
            scope = viewModelScope,
            started = SharingStarted.WhileSubscribed(5000),
            initialValue = emptyList()
        )
    
    val cancelledRefunds: StateFlow<List<Refund>> = repository
        .getRefundsByStatus(RefundStatus.CANCELLED)
        .stateIn(
            scope = viewModelScope,
            started = SharingStarted.WhileSubscribed(5000),
            initialValue = emptyList()
        )
    
    fun selectTab(index: Int) {
        _selectedTabIndex.value = index
    }
    
    fun deleteRefund(refund: Refund) {
        viewModelScope.launch {
            repository.deleteRefund(refund)
        }
    }
    
    fun updateRefundStatus(refund: Refund, newStatus: RefundStatus) {
        viewModelScope.launch {
            repository.updateRefund(refund.copy(refundStatus = newStatus))
        }
    }
}
