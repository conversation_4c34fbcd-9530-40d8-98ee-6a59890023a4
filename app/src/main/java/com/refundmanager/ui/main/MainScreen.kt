package com.refundmanager.ui.main

import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.pager.HorizontalPager
import androidx.compose.foundation.pager.rememberPagerState
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.Add
import androidx.compose.material3.ExperimentalMaterial3Api
import androidx.compose.material3.Icon
import androidx.compose.material3.IconButton
import androidx.compose.material3.Scaffold
import androidx.compose.material3.Tab
import androidx.compose.material3.TabRow
import androidx.compose.material3.Text
import androidx.compose.material3.TopAppBar
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.compose.runtime.rememberCoroutineScope
import androidx.compose.ui.Modifier
import androidx.compose.ui.platform.LocalContext
import androidx.lifecycle.viewmodel.compose.viewModel
import androidx.navigation.NavController
import com.refundmanager.RefundManagerApplication
import com.refundmanager.ui.viewmodel.MainViewModel
import com.refundmanager.ui.viewmodel.ViewModelFactory
import kotlinx.coroutines.launch

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun MainScreen(
    navController: NavController,
    viewModel: MainViewModel = viewModel(
        factory = ViewModelFactory(
            (LocalContext.current.applicationContext as RefundManagerApplication).appContainer.refundRepository
        )
    )
) {
    val selectedTabIndex by viewModel.selectedTabIndex.collectAsState()
    val pendingRefunds by viewModel.pendingRefunds.collectAsState()
    val refundedRefunds by viewModel.refundedRefunds.collectAsState()
    val cancelledRefunds by viewModel.cancelledRefunds.collectAsState()
    
    val pagerState = rememberPagerState(pageCount = { 3 })
    val coroutineScope = rememberCoroutineScope()
    
    val tabs = listOf("未退款", "已退款", "已取消")
    
    LaunchedEffect(selectedTabIndex) {
        pagerState.animateScrollToPage(selectedTabIndex)
    }
    
    LaunchedEffect(pagerState.currentPage) {
        viewModel.selectTab(pagerState.currentPage)
    }
    
    Scaffold(
        topBar = {
            TopAppBar(
                title = { Text("退款管理") },
                actions = {
                    IconButton(
                        onClick = { navController.navigate("add_refund") }
                    ) {
                        Icon(
                            imageVector = Icons.Default.Add,
                            contentDescription = "添加退款"
                        )
                    }
                }
            )
        }
    ) { paddingValues ->
        Column(
            modifier = Modifier
                .fillMaxSize()
                .padding(paddingValues)
        ) {
            TabRow(
                selectedTabIndex = selectedTabIndex,
                modifier = Modifier.fillMaxWidth()
            ) {
                tabs.forEachIndexed { index, title ->
                    Tab(
                        selected = selectedTabIndex == index,
                        onClick = {
                            viewModel.selectTab(index)
                            coroutineScope.launch {
                                pagerState.animateScrollToPage(index)
                            }
                        },
                        text = { Text(title) }
                    )
                }
            }
            
            HorizontalPager(
                state = pagerState,
                modifier = Modifier.fillMaxSize()
            ) { page ->
                when (page) {
                    0 -> RefundListScreen(
                        refunds = pendingRefunds,
                        onRefundClick = { /* TODO: Navigate to detail */ },
                        onRefundDelete = { viewModel.deleteRefund(it) },
                        onRefundStatusChange = { refund, status -> 
                            viewModel.updateRefundStatus(refund, status) 
                        }
                    )
                    1 -> RefundListScreen(
                        refunds = refundedRefunds,
                        onRefundClick = { /* TODO: Navigate to detail */ },
                        onRefundDelete = { viewModel.deleteRefund(it) },
                        onRefundStatusChange = { refund, status -> 
                            viewModel.updateRefundStatus(refund, status) 
                        }
                    )
                    2 -> RefundListScreen(
                        refunds = cancelledRefunds,
                        onRefundClick = { /* TODO: Navigate to detail */ },
                        onRefundDelete = { viewModel.deleteRefund(it) },
                        onRefundStatusChange = { refund, status -> 
                            viewModel.updateRefundStatus(refund, status) 
                        }
                    )
                }
            }
        }
    }
}
